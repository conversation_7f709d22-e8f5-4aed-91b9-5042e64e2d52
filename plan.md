# Performance Improvement Plan for High RPS Server

## 1. Goal

The primary objective is to optimize the Go RPS server to handle a very high request rate (targeting 100k+ RPS) and high connection churn. The focus is on minimizing latency and CPU usage on the request/response "hot path" and during connection setup/teardown.

## 2. Proposed Optimizations

The following optimizations are proposed, ordered by their expected impact for the target workload.

### 2.1. Connection Map Sharding

**Problem:** The current implementation uses a single `sync.Map` (`connTracker`) to track all active connections. Under high connection churn (many `OnOpen` and `OnClose` events per second), all CPU cores will contend for access to this single map, creating a potential scalability bottleneck.

**Solution:** We will replace the single `sync.Map` with a sharded map. This involves creating a fixed-size array of maps (e.g., 256 shards). A connection is assigned to a shard based on its file descriptor. This distributes the lock contention across multiple maps, allowing for much higher concurrent access.

**Architecture:**

```mermaid
graph TD
    subgraph Before (Single Map)
        C1(CPU 1) -->|OnOpen/OnClose| M(sync.Map)
        C2(CPU 2) -->|OnOpen/OnClose| M
        C3(CPU 3) -->|OnOpen/OnClose| M
        C4(CPU 4) -->|OnOpen/OnClose| M
    end

    subgraph After (Sharded Map)
        C1_A(CPU 1) -->|fd % 4| S0(Shard 0)
        C2_A(CPU 2) -->|fd % 4| S1(Shard 1)
        C3_A(CPU 3) -->|fd % 4| S2(Shard 2)
        C4_A(CPU 4) -->|fd % 4| S3(Shard 3)
    end
```

**Implementation Steps:**

1.  **Define `shardedConnTracker`:** Create a new struct that holds an array of shards. Each shard will contain a `sync.Map` and a `sync.RWMutex` for operations that need to iterate (like `OnTick`).
2.  **Initialize Shards:** In the `rpsServer` struct, replace `connTracker` with the new sharded map type, initialized with a power-of-two number of shards (e.g., 256) for efficient modulo operations.
3.  **Update Handlers:** Modify `OnOpen`, `OnClose`, `OnTraffic`, and `OnTick` to first select the correct shard using `fd % numShards` and then perform the operation on that specific shard's map.

---

### 2.2. Profile-Guided Optimization (PGO)

**Problem:** The Go compiler generates optimized code, but it doesn't know which parts of our application are executed most frequently under a real-world load. The request-handling "hot path" could be made even faster if the compiler had this information.

**Solution:** Use Go's Profile-Guided Optimization (PGO). This process involves:
1.  Running the application under a realistic, high-RPS load.
2.  Collecting a CPU profile (`pprof`) during this run.
3.  Feeding this profile back to the Go compiler during the next build.

The compiler will use the profile data to make more aggressive and intelligent optimization decisions, such as inlining functions on the hot path and improving register allocation.

**Implementation Steps:**

1.  **Enable `pprof`:** Add an HTTP endpoint to the server to expose profiling data. This is typically done by importing `net/http/pprof`. This can be run on a separate port or conditionally compiled.
2.  **Generate Profile:** Run the server and apply a high-RPS load using a tool like `wrk` or `bombardier`. Capture a CPU profile: `go tool pprof http://localhost:6060/debug/pprof/profile?seconds=30`.
3.  **Build with PGO:** Save the collected profile as `default.pgo` in the project's root directory. Build the application using `go build -pgo=auto`. The Go toolchain will automatically find and use the `default.pgo` file.

---

### 2.3. UI Rendering Optimization

**Problem:** The `updateUI` function, which refreshes the console statistics, makes numerous calls to `fmt.Printf`. Each call results in a separate `write` syscall to the standard output. While not on the main request path, this can create unnecessary syscall overhead and CPU usage that could be used for handling requests.

**Solution:** Buffer the entire UI output in memory before writing it to the console in a single operation. This consolidates many small writes into one larger, more efficient write, significantly reducing the number of syscalls.

**Implementation Steps:**

1.  **Use `strings.Builder`:** In the `updateUI` function, create an instance of `strings.Builder`.
2.  **Redirect Output:** Replace all `fmt.Printf(...)` calls with `fmt.Fprintf(&builder, ...)` to write to the in-memory builder instead of the console.
3.  **Single Write:** At the very end of the function, make a single call to `fmt.Print(builder.String())` to write the complete UI to the console at once.

---

### 2.4. Efficient Timeout Handling with a Timing Wheel

**Problem:** The current `OnTick` function iterates through every connection in the `connTracker` map to check for idle timeouts. For a workload with high connection churn, this might be acceptable. However, if the server ever needs to handle a large number of *concurrent* connections (even short-lived ones), this linear scan will become a significant source of periodic CPU usage.

**Solution:** As a forward-looking optimization, replace the linear scan with a **Timing Wheel**. A timing wheel is a data structure that provides O(1) complexity for adding/refreshing timers and amortized O(1) for finding expired timers. This is vastly more scalable than a linear scan.

**Implementation Steps (High-Level):**

1.  **Integrate a Timing Wheel:** Add a proven timing wheel library (e.g., from an existing open-source project) to the server.
2.  **Manage Timers:**
    *   **`OnOpen`**: When a connection is opened, add it to the timing wheel with the specified `connectionTimeout`.
    *   **`OnTraffic`**: When traffic is received, update the connection's position in the timing wheel to reset its timeout.
    *   **`OnClose`**: Remove the connection from the timing wheel.
3.  **Refactor `OnTick`**: The `OnTick` function will no longer iterate the map. Instead, it will be driven by the timing wheel, which will efficiently provide a list of connections that have expired in the last tick interval.

## 3. Summary

By implementing these changes, we will significantly enhance the server's performance for its target workload. Sharding the connection map will address the primary bottleneck of high connection churn, while PGO will provide a general performance uplift on the hot path. The UI and timeout optimizations provide additional robustness and efficiency, ensuring the server remains performant as it scales.